"""
海天AI纳斯达克交易系统 - 全局依赖注入
基于: 项目手册4.1节MVP版本技术栈配置
创建日期: 2025年7月31日
技术栈: FastAPI 0.116.1 + Python 3.13.2
功能: 数据库会话管理、认证依赖、配置对象注入、日志记录器配置
"""

from typing import Generator, Optional, Dict, Any
import logging
from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db, get_async_db, SessionLocal, AsyncSessionLocal
from app.core.settings import settings
from app.core.security import decode_token, TokenData
from app.core.exceptions import (
    AuthenticationException,
    AuthorizationException,
    DatabaseException
)

# =============================================================================
# 安全认证依赖
# =============================================================================

security = HTTPBearer(auto_error=False)  # 不自动抛出错误

def get_current_user_token(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> str:
    """
    获取当前用户的JWT令牌

    Args:
        credentials: HTTP认证凭据

    Returns:
        str: JWT令牌

    Raises:
        AuthenticationException: 认证失败
    """
    if not credentials:
        raise AuthenticationException("缺少认证凭据")

    return credentials.credentials


def get_current_user(
    token: str = Depends(get_current_user_token)
) -> TokenData:
    """
    获取当前用户信息
    
    Args:
        token: JWT令牌
        
    Returns:
        TokenData: 用户令牌数据
        
    Raises:
        AuthenticationException: 认证失败
    """
    try:
        return decode_token(token)
    except HTTPException as e:
        raise AuthenticationException(f"令牌验证失败: {e.detail}")


def get_current_active_user(
    current_user: TokenData = Depends(get_current_user)
) -> TokenData:
    """
    获取当前活跃用户
    
    Args:
        current_user: 当前用户令牌数据
        
    Returns:
        TokenData: 活跃用户令牌数据
        
    Raises:
        AuthenticationException: 用户未激活
    """
    # 这里可以添加用户状态检查逻辑
    # 例如检查用户是否被禁用、是否需要重新验证等
    return current_user


def require_scopes(required_scopes: list[str]):
    """
    权限范围检查装饰器工厂
    
    Args:
        required_scopes: 需要的权限范围列表
        
    Returns:
        function: 权限检查依赖函数
    """
    def check_scopes(
        current_user: TokenData = Depends(get_current_active_user)
    ) -> TokenData:
        """
        检查用户权限范围
        
        Args:
            current_user: 当前用户
            
        Returns:
            TokenData: 用户数据
            
        Raises:
            AuthorizationException: 权限不足
        """
        user_scopes = set(current_user.scopes)
        required_scopes_set = set(required_scopes)
        
        if not required_scopes_set.issubset(user_scopes):
            missing_scopes = required_scopes_set - user_scopes
            raise AuthorizationException(
                f"权限不足，缺少权限: {', '.join(missing_scopes)}"
            )
        
        return current_user
    
    return check_scopes


# =============================================================================
# 数据库会话依赖
# =============================================================================

def get_database_session() -> Generator[Session, None, None]:
    """
    获取数据库会话（同步）

    Yields:
        Session: 数据库会话

    Raises:
        DatabaseException: 数据库连接失败
    """
    try:
        db = next(get_db())
        yield db
    except (ConnectionError, OSError, ImportError) as e:
        # 只捕获真正的数据库连接错误
        raise DatabaseException(f"数据库连接失败: {str(e)}")
    except Exception:
        # 其他异常直接重新抛出，不要转换为DatabaseException
        raise


async def get_async_database_session() -> AsyncSession:
    """
    获取异步数据库会话
    
    Returns:
        AsyncSession: 异步数据库会话
        
    Raises:
        DatabaseException: 数据库连接失败
    """
    try:
        async for db in get_async_db():
            return db
    except Exception as e:
        raise DatabaseException(f"异步数据库连接失败: {str(e)}")


# =============================================================================
# 配置对象依赖
# =============================================================================

def get_settings():
    """
    获取应用配置
    
    Returns:
        Settings: 应用配置对象
    """
    return settings


def get_database_settings():
    """
    获取数据库配置
    
    Returns:
        DatabaseSettings: 数据库配置对象
    """
    return settings.database


def get_security_settings():
    """
    获取安全配置
    
    Returns:
        SecuritySettings: 安全配置对象
    """
    return settings.security


def get_ai_model_settings():
    """
    获取AI模型配置
    
    Returns:
        AIModelSettings: AI模型配置对象
    """
    return settings.ai_models


def get_trading_settings():
    """
    获取交易配置
    
    Returns:
        TradingSettings: 交易配置对象
    """
    return settings.trading


def get_qmt_settings():
    """
    获取QMT配置
    
    Returns:
        QMTSettings: QMT配置对象
    """
    return settings.qmt


# =============================================================================
# 日志记录器依赖
# =============================================================================

def get_logger(name: str = __name__) -> logging.Logger:
    """
    获取日志记录器
    
    Args:
        name: 日志记录器名称
        
    Returns:
        logging.Logger: 日志记录器
    """
    logger = logging.getLogger(name)
    
    # 如果日志记录器还没有配置处理器，则添加基本配置
    if not logger.handlers:
        handler = logging.StreamHandler()
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        handler.setFormatter(formatter)
        logger.addHandler(handler)
        logger.setLevel(logging.INFO)
    
    return logger


def get_api_logger() -> logging.Logger:
    """
    获取API专用日志记录器
    
    Returns:
        logging.Logger: API日志记录器
    """
    return get_logger("api")


def get_trading_logger() -> logging.Logger:
    """
    获取交易专用日志记录器
    
    Returns:
        logging.Logger: 交易日志记录器
    """
    return get_logger("trading")


def get_ai_logger() -> logging.Logger:
    """
    获取AI专用日志记录器
    
    Returns:
        logging.Logger: AI日志记录器
    """
    return get_logger("ai")


# =============================================================================
# 缓存客户端依赖（预留）
# =============================================================================

def get_cache_client():
    """
    获取缓存客户端
    
    Returns:
        Any: 缓存客户端（Redis等）
        
    Note:
        当前返回None，后续可以集成Redis客户端
    """
    # TODO: 集成Redis客户端
    return None


# =============================================================================
# 外部服务客户端依赖（预留）
# =============================================================================

def get_qmt_client():
    """
    获取QMT客户端
    
    Returns:
        Any: QMT客户端
        
    Note:
        当前返回None，后续集成QMT客户端
    """
    # TODO: 集成QMT客户端
    return None


def get_ai_model_client():
    """
    获取AI模型客户端
    
    Returns:
        Any: AI模型客户端
        
    Note:
        当前返回None，后续集成AI模型客户端
    """
    # TODO: 集成AI模型客户端
    return None


# =============================================================================
# 请求上下文依赖
# =============================================================================

def get_request_id() -> Optional[str]:
    """
    获取请求ID
    
    Returns:
        Optional[str]: 请求ID
        
    Note:
        当前返回None，后续可以在中间件中设置
    """
    # TODO: 在中间件中设置请求ID
    return None


def get_user_context(
    current_user: TokenData = Depends(get_current_active_user),
    request_id: Optional[str] = Depends(get_request_id)
) -> Dict[str, Any]:
    """
    获取用户上下文信息
    
    Args:
        current_user: 当前用户
        request_id: 请求ID
        
    Returns:
        Dict[str, Any]: 用户上下文
    """
    return {
        "user_id": current_user.user_id,
        "username": current_user.username,
        "scopes": current_user.scopes,
        "request_id": request_id
    }
