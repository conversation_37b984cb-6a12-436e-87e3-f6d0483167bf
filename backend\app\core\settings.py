"""
Settings and configuration classes for AI Trading System.
"""

import os
import yaml
from pathlib import Path
from typing import Dict, List, Optional, Any
from pydantic import Field, field_validator, model_validator
from pydantic_settings import BaseSettings


class DatabaseSettings(BaseSettings):
    """PostgreSQL数据库配置"""

    host: str = "localhost"
    port: int = 5432
    user: str = "postgres"
    password: Optional[str] = None
    database: str = "ai_trading"

    # 连接池配置
    pool_size: int = 20
    max_overflow: int = 30
    pool_timeout: int = 30
    pool_recycle: int = 3600

    # SSL配置
    ssl_mode: str = "prefer"

    model_config = {
        "env_prefix": "DB_",
        "extra": "ignore"
    }
    
    @property
    def database_url(self) -> str:
        """构建数据库连接URL"""
        return f"postgresql://{self.user}:{self.password}@{self.host}:{self.port}/{self.database}"
    
    model_config = {
        "env_prefix": "DB_",
        "extra": "ignore"
    }


class AIModelSettings(BaseSettings):
    """AI模型配置"""
    
    # OpenAI GPT-4配置
    openai_api_key: Optional[str] = None
    openai_base_url: str = "https://api.openai.com/v1"
    openai_model: str = "gpt-4"
    openai_timeout: int = 60

    # Anthropic Claude配置
    anthropic_api_key: Optional[str] = None
    anthropic_base_url: str = "https://api.anthropic.com"
    anthropic_model: str = "claude-3-sonnet-20240229"
    anthropic_timeout: int = 60

    # Google Gemini配置
    google_api_key: Optional[str] = None
    google_model: str = "gemini-pro"
    google_timeout: int = 60

    # 通用配置
    max_concurrent_requests: int = 10
    request_retry_times: int = 3
    request_retry_delay: float = 1.0
    
    model_config = {
        "env_prefix": "AI_",
        "extra": "ignore"
    }


class QMTSettings(BaseSettings):
    """QMT量化交易接口配置"""
    
    # QMT连接配置
    host: str = "localhost"
    port: int = 58610
    username: Optional[str] = None
    password: Optional[str] = None

    # 交易配置
    account_id: Optional[str] = None
    session_timeout: int = 300
    max_concurrent_orders: int = 3
    order_timeout: int = 10

    # 数据配置
    data_update_interval: int = 1
    tick_data_buffer_size: int = 1000
    
    model_config = {
        "env_prefix": "QMT_",
        "extra": "ignore"
    }


class TradingSettings(BaseSettings):
    """交易系统配置"""
    
    # 全局交易参数
    total_capital: float = ********.0  # 1000万
    max_usable_capital_ratio: float = 0.8
    max_single_trade_ratio: float = 0.1
    max_single_stock_ratio: float = 0.15

    # AI交易员数量
    ai_trader_count: int = 8

    # 风险控制
    daily_loss_limit_ratio: float = 0.03
    max_drawdown_ratio: float = 0.08

    # 交易时间
    market_open_time: str = "09:30"
    market_close_time: str = "15:00"
    daily_review_time: str = "15:10"

    # 数据更新频率
    data_snapshot_interval: int = 1
    
    model_config = {
        "env_prefix": "TRADING_",
        "extra": "ignore"
    }


class SecuritySettings(BaseSettings):
    """安全配置"""
    
    # JWT配置
    secret_key: str = "dev-secret-key-change-in-production"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    refresh_token_expire_days: int = 7

    # API安全
    api_rate_limit: int = 100
    api_rate_limit_window: int = 60

    # 数据加密
    encryption_key: Optional[str] = None

    # CORS配置
    cors_origins: List[str] = ["http://localhost:3000"]
    
    @field_validator('cors_origins', mode='before')
    @classmethod
    def parse_cors_origins(cls, v):
        if isinstance(v, str):
            return [origin.strip() for origin in v.split(',')]
        return v
    
    model_config = {
        "env_prefix": "SECURITY_",
        "extra": "ignore"
    }


class LoggingSettings(BaseSettings):
    """日志配置"""
    
    level: str = "INFO"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

    # 文件日志
    file_enabled: bool = True
    file_path: str = "logs/app.log"
    file_max_size: str = "10MB"
    file_backup_count: int = 5

    # 控制台日志
    console_enabled: bool = True
    
    model_config = {
        "env_prefix": "LOG_",
        "extra": "ignore"
    }


class RedisSettings(BaseSettings):
    """Redis缓存配置"""
    
    host: str = "localhost"
    port: int = 6379
    password: Optional[str] = None
    database: int = 0

    # 连接池配置
    max_connections: int = 20
    connection_timeout: int = 5

    # 缓存配置
    default_ttl: int = 3600
    
    @property
    def redis_url(self) -> str:
        """构建Redis连接URL"""
        if self.password:
            return f"redis://:{self.password}@{self.host}:{self.port}/{self.database}"
        return f"redis://{self.host}:{self.port}/{self.database}"
    
    model_config = {
        "env_prefix": "REDIS_",
        "extra": "ignore"
    }


class Settings(BaseSettings):
    """主配置类"""
    
    # 应用基础配置
    app_name: str = "AI Trading System"
    app_version: str = "1.0.0"
    environment: str = "development"
    debug: bool = False

    # 服务配置
    host: str = "0.0.0.0"
    port: int = 8000
    
    # 子配置
    database: DatabaseSettings = DatabaseSettings()
    ai_models: AIModelSettings = AIModelSettings()
    qmt: QMTSettings = QMTSettings()
    trading: TradingSettings = TradingSettings()
    security: SecuritySettings = SecuritySettings()
    logging: LoggingSettings = LoggingSettings()
    redis: RedisSettings = RedisSettings()
    
    # 配置文件路径
    config_dir: str = "config"
    
    model_config = {
        "env_file": "infrastructure/.env",
        "env_file_encoding": "utf-8",
        "case_sensitive": False,
        "extra": "ignore"
    }
    
    def load_yaml_configs(self) -> None:
        """加载YAML配置文件"""
        config_path = Path(self.config_dir)
        if not config_path.exists():
            return
        
        # 加载各种配置文件
        config_files = {
            "ai_models.yaml": self.ai_models,
            "trading.yaml": self.trading,
            "security.yaml": self.security,
        }
        
        for filename, config_obj in config_files.items():
            file_path = config_path / filename
            if file_path.exists():
                with open(file_path, 'r', encoding='utf-8') as f:
                    yaml_data = yaml.safe_load(f)
                    if yaml_data:
                        # 更新配置对象
                        for key, value in yaml_data.items():
                            if hasattr(config_obj, key):
                                setattr(config_obj, key, value)
    
    @model_validator(mode='after')
    def validate_settings(self):
        """配置验证"""
        # 在生产环境中验证必需的敏感信息
        # 开发环境可以使用默认值
        return self


# 创建全局配置实例
settings = Settings()
