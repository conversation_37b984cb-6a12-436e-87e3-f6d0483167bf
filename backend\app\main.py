"""
海天AI纳斯达克交易系统 - 主应用入口
基于: 项目手册4.1节MVP版本技术栈配置
创建日期: 2025年7月13日
技术栈: FastAPI 0.116.1 + Python 3.13.2
"""

from contextlib import asynccontextmanager
from fastapi import FastAPI, Request, Response
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException
from starlette.middleware.base import BaseHTTPMiddleware
import uvicorn
import os
import time
import uuid
import logging
from datetime import datetime
from typing import Callable

from app.core.settings import settings
from app.core.exceptions import (
    AITradingException,
    ai_trading_exception_handler,
    http_exception_handler,
    validation_exception_handler,
    general_exception_handler
)
from app.api.v1 import api_router

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# =============================================================================
# 应用生命周期事件
# =============================================================================

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理器"""
    # 启动时的初始化操作
    logger.info("🚀 海天AI纳斯达克交易系统启动中...")
    logger.info(f"📊 环境: {settings.environment}")
    logger.info(f"🔗 数据库: {settings.database.host}:{settings.database.port}")
    logger.info(f"🔐 安全配置: JWT算法={settings.security.algorithm}")
    logger.info(f"🌐 CORS允许源: {settings.security.cors_origins}")
    logger.info("✅ 系统启动完成")

    yield

    # 关闭时的清理操作
    logger.info("🛑 海天AI纳斯达克交易系统正在关闭...")
    # 这里可以添加清理逻辑，如关闭数据库连接、停止后台任务等
    logger.info("✅ 系统已安全关闭")

# =============================================================================
# 自定义中间件类
# =============================================================================

class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """请求日志中间件"""

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # 生成请求ID
        request_id = str(uuid.uuid4())
        request.state.request_id = request_id

        # 记录请求开始
        start_time = time.time()
        logger.info(f"Request started: {request.method} {request.url.path}", extra={
            "request_id": request_id,
            "method": request.method,
            "path": request.url.path,
            "query_params": str(request.query_params),
            "client_ip": request.client.host if request.client else "unknown"
        })

        # 处理请求
        response = await call_next(request)

        # 记录请求完成
        process_time = time.time() - start_time
        logger.info(f"Request completed: {request.method} {request.url.path}", extra={
            "request_id": request_id,
            "method": request.method,
            "path": request.url.path,
            "status_code": response.status_code,
            "process_time": f"{process_time:.4f}s"
        })

        # 添加响应头
        response.headers["X-Request-ID"] = request_id
        response.headers["X-Process-Time"] = f"{process_time:.4f}"

        return response


class PerformanceMonitoringMiddleware(BaseHTTPMiddleware):
    """性能监控中间件"""

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        start_time = time.time()

        # 处理请求
        response = await call_next(request)

        # 计算处理时间
        process_time = time.time() - start_time

        # 如果处理时间过长，记录警告
        if process_time > 5.0:  # 5秒阈值
            logger.warning(f"Slow request detected: {request.method} {request.url.path}", extra={
                "request_id": getattr(request.state, "request_id", "unknown"),
                "method": request.method,
                "path": request.url.path,
                "process_time": f"{process_time:.4f}s",
                "status_code": response.status_code
            })

        return response


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """安全头中间件"""

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        response = await call_next(request)

        # 添加安全头
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"

        return response


# 创建FastAPI应用实例
app = FastAPI(
    title="海天AI纳斯达克交易系统",
    description="AI驱动的纳斯达克实时量化交易系统API",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    openapi_url="/api/v1/openapi.json",
    lifespan=lifespan
)

# =============================================================================
# 中间件配置（按执行顺序添加）
# =============================================================================

# 1. 安全头中间件（最外层）
app.add_middleware(SecurityHeadersMiddleware)

# 2. 性能监控中间件
app.add_middleware(PerformanceMonitoringMiddleware)

# 3. 请求日志中间件
app.add_middleware(RequestLoggingMiddleware)

# 4. CORS中间件（使用配置文件中的设置）
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.security.cors_origins,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
    expose_headers=["X-Request-ID", "X-Process-Time"]
)

# 根路径健康检查
@app.get("/")
async def root():
    """系统健康检查和基本信息"""
    return {
        "message": "海天AI纳斯达克交易系统",
        "version": "1.0.0",
        "status": "running",
        "timestamp": datetime.now().isoformat(),
        "environment": os.getenv("ENVIRONMENT", "development"),
        "docs": "/docs",
        "redoc": "/redoc"
    }

# 健康检查端点
@app.get("/health")
async def health_check():
    """详细的健康检查"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "services": {
            "api": "running",
            "database": "checking...",  # 后续会添加数据库连接检查
            "ai_traders": "initializing",
            "trading_engine": "standby"
        },
        "version": "1.0.0"
    }

# API版本信息
@app.get("/api/v1/info")
async def api_info():
    """API版本和功能信息"""
    return {
        "api_version": "v1",
        "features": [
            "AI交易员管理",
            "实时行情数据",
            "交易执行引擎",
            "风险控制系统",
            "性能监控"
        ],
        "endpoints": {
            "traders": "/api/v1/traders",
            "trading": "/api/v1/trading",
            "market_data": "/api/v1/market",
            "monitoring": "/api/v1/monitoring"
        }
    }

# =============================================================================
# 异常处理器配置
# =============================================================================

# 注册自定义异常处理器
app.add_exception_handler(AITradingException, ai_trading_exception_handler)
app.add_exception_handler(StarletteHTTPException, http_exception_handler)
app.add_exception_handler(RequestValidationError, validation_exception_handler)
app.add_exception_handler(Exception, general_exception_handler)

# =============================================================================
# API路由注册
# =============================================================================

# 注册API v1路由
app.include_router(
    api_router,
    prefix="/api/v1",
    responses={
        404: {"description": "Not found"},
        422: {"description": "Validation Error"},
        500: {"description": "Internal Server Error"}
    }
)



# =============================================================================
# 主程序入口
# =============================================================================

if __name__ == "__main__":
    # 开发环境直接运行
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=settings.port,
        reload=settings.environment == "development",
        log_level="info",
        access_log=True
    )
